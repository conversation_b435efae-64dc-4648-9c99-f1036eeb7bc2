import {shortUrlModel} from '../models/shorturl.model.js'
import { ConflictError } from '../utils/errorhandler.js'

export const saveShortUrl= async (shortURL,longURL,userID)=>{
    try{
    const newURL= new shortUrlModel({
        full_url:longURL,
        short_Url:shortURL
    })
    if(userID){
        newURL.user=userID
    }
    await newURL.save()
}
catch(err){
    if(err.code===11000){
        throw new ConflictError('Short URL already exists')
    }
    throw new Error(err)
}
}
export const findUrlFromShortUrl= async (shortURL)=>{
    return await shortUrlModel.findOneAndUpdate({short_Url:shortURL},{$inc:{clicks:1}})
}