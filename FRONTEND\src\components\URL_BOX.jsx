import { useQuery } from '@tanstack/react-query'
import axios from 'axios'
import React, { useState } from 'react'
import { createShortUrl } from '../api/short_Url.api'

const URL_BOX = () => {
    
    const[url, setUrl] = useState('')
    const[shortUrl, setShortUrl] = useState('')
    const[isCopied, setIsCopied] = useState(false)
    const handleSubmit=async (event)=>{
        event.preventDefault()
        const {data}= await createShortUrl(url)
        setShortUrl(data)
        

    }

  

  return (
    <form onSubmit={handleSubmit} className='
      /* Mobile First - Base styles */
      w-full max-w-sm mx-auto
      min-h-[300px]
      bg-gradient-to-r from-white to-gray-100
      rounded-xl shadow-xl border-2 border-pink-300
      flex flex-col items-center
      p-4 sm:p-6
      mt-4 sm:mt-6 md:mt-8 lg:mt-10

      /* Small screens (sm: 640px+) */
      sm:max-w-md sm:min-h-[320px]

      /* Medium screens (md: 768px+) */
      md:max-w-lg md:min-h-[350px] md:p-8

      /* Large screens (lg: 1024px+) */
      lg:max-w-xl lg:min-h-[380px]

      /* Extra large screens (xl: 1280px+) */
      xl:max-w-2xl xl:min-h-[400px]
    '>
        <h1 className='
          text-xl font-bold text-pink-500 mt-2 text-center
          sm:text-2xl sm:mt-3
          md:text-3xl md:mt-4
          lg:text-3xl
          xl:text-4xl
        '>
          URL Shortener
        </h1>

       <div className='
         w-full flex justify-start flex-col
         mt-6 sm:mt-7 md:mt-8 lg:mt-9
       '>
        <h2 className='
          text-xs font-semibold text-pink-500 mb-2
          sm:text-sm sm:mb-3
          md:text-base md:mb-3
          lg:text-base
        '>
          Enter Your URL
        </h2>

        <input
          type="url"
          required
          className='
            h-10 w-full rounded border-2 border-pink-300
            p-2 outline-pink-500 text-sm text-gray-600
            focus:border-pink-500 focus:ring-2 focus:ring-pink-200
            transition-all duration-200
            sm:h-11 sm:text-base sm:p-3
            md:h-12 md:text-base
            lg:h-12
          '
          value={url}
          onChange={(event)=>{
            setUrl(event.target.value)
          }}
          placeholder='https://example.com'
        />

        <button className='
          h-10 w-full rounded mt-3
          bg-pink-500 text-white font-bold
          hover:bg-pink-600 cursor-pointer shadow-xl
          active:scale-95 transition-all duration-150
          focus:outline-none focus:ring-2 focus:ring-pink-300
          sm:h-11 sm:mt-4 sm:text-base
          md:h-12 md:text-base
          lg:h-12
        '>
          Shorten URL
        </button>

        {shortUrl && (
            <div className='
            w-full flex justify-start flex-col
            mt-6 sm:mt-7 md:mt-8 lg:mt-9
          '>
            <h2 className='
              text-xs font-semibold text-pink-500 mb-2
              sm:text-sm sm:mb-3
              md:text-base md:mb-3
              lg:text-base
            '>
               Your Shortened URL
            </h2>
            <div className='flex flex-row justify-between '>
            <input
              type="url"
              className='
                h-10 w-[80%] rounded border-2 border-pink-300
                p-2 outline-pink-500 text-sm text-gray-600
                focus:border-pink-500 focus:ring-2 focus:ring-pink-200
                transition-all duration-200
                sm:h-11 sm:text-base sm:p-3
                md:h-12 md:text-base
                lg:h-12
              '
              value={shortUrl}
              onChange={(event)=>{
                setShortUrl(event.target.value)
              }}
              placeholder='https://example.com'
            />
            <button onClick={()=>{
                navigator.clipboard.writeText(shortUrl)
                setIsCopied(true)
            }} className='
          h-10 w-1/5 rounded bg-pink-500 text-white font-bold
          hover:bg-pink-600 cursor-pointer shadow-xl
          active:scale-95 transition-all duration-150
          focus:outline-none focus:ring-2 focus:ring-pink-300
          sm:h-11  sm:text-base
          md:h-12 md:text-base
          lg:h-12
        '>
            {isCopied ? 'Copied!' : 'Copy'}
        </button>
            </div>
          </div>
        )}

       </div>
    </form>
  )
}

export default URL_BOX
