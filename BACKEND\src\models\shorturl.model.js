import mongoose from 'mongoose'// importing mongoose


const shortUrlSchema = new mongoose.Schema({
    full_url: {
        type: String,
        required: true,
        unique: true,
        index: true
    },
    short_Url: {
        type: String,
        required: true
    },
    clicks: {
        type: Number,
        required: true,
        default: 0
    },
    user:{
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User'
    }
})

const shortUrlModel = mongoose.model('ShortUrl', shortUrlSchema)

export {shortUrlModel}