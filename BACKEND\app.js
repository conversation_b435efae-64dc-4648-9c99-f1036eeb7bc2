import express from 'express'// importing express
import {nanoid} from 'nanoid'// importing nanoid
import {connectToDB} from './db.js'// importing connectToDB
const app=express();// creating an instance of express


app.use(express.json())// middleware to parse json data
app.use(express.urlencoded({extended:true}))// middleware to parse urlencoded data

//sample route
app.post('/api/create',(req,res)=>{
    const{url}=req.body
    console.log(url)
    res.send(nanoid(100))
})

//listening to the port
app.listen(3000 ,()=>{
    connectToDB()
    console.log('server is running on port 3000');
})



//GET-REDIRECTION
//POST-CREATE SHORT URL



