import { nanoid } from "nanoid"
import { generateNanoid } from "../utils/helper.js"
import { createShortUrlWithoutUser, createShortUrlWithUser } from "../services/short_Url.service.js"
import { findUrlFromShortUrl } from "../dao/short_Url.js"

export const createShortUrl= async(req,res,next)=>{

    try{
    const{url}=req.body
    const shortURL= await createShortUrlWithoutUser(url)
    res.status(200).json({shortURL:process.env.APP_URL + shortURL})
    }
    catch(err){
        next(err)
    }
}

export const redirectFRomShortUrl=async (req,res,next)=>{
    try{
    const {id}=req.params
    const url=await findUrlFromShortUrl(id)
    if(!url){
        throw new Error('URL not found')
    }
      res.redirect(url.full_url)
}
    catch(err){
        next(err)
    }
  
}
    
