import { nanoid } from "nanoid"
import { generateNanoid } from "../utils/helper.js"
import {shortUrlModel} from '../models/shorturl.model.js'
import { saveShortUrl } from "../dao/short_Url.js"

export const createShortUrlWithoutUser= async (url)=>{
     const shortURL= generateNanoid(7)
     if(!shortURL) {
        throw new Error('Short URL not generated')
     }
     
    await saveShortUrl(shortURL,url)
    return shortURL
}

export const createShortUrlWithUser= async (url,userID)=>{
     const shortURL= generateNanoid(7)
     
    await saveShortUrl(shortURL,url, userID)
    return shortURL
}